// pages/add-coffee/add-coffee.js
const app = getApp()

Page({
  data: {
    isEdit: false,
    coffeeId: '',
    formData: {
      brand: '',
      name: '',
      variety: '',
      origin: '',
      estate: '',
      process: '',
      roastLevel: '',
      flavor: '',
      price: '',
      grindSize: '',
      rating: 0,
      review: '',
      purchaseLink: ''
    },
    roastLevels: ['浅烘', '中浅烘', '中烘', '中深烘', '深烘'],
    processOptions: ['水洗', '日晒', '蜜处理', '湿刨法', '厌氧发酵', '碳酸浸渍', '其他'],
    loading: false
  },

  onLoad(options) {
    // 检查是否为编辑模式
    if (options.id) {
      this.setData({
        isEdit: true,
        coffeeId: options.id
      })
      this.loadCoffeeData(options.id)
    }
  },

  // 加载咖啡数据（编辑模式）
  async loadCoffeeData(coffeeId) {
    try {
      wx.showLoading({
        title: '加载中...'
      })

      const db = wx.cloud.database()
      const result = await db.collection('coffee_atlas').doc(coffeeId).get()
      
      if (result.data) {
        this.setData({
          formData: result.data
        })
      }
    } catch (error) {
      console.error('加载咖啡数据失败', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 选择烘焙度
  onRoastLevelChange(e) {
    const index = e.detail.value
    this.setData({
      'formData.roastLevel': this.data.roastLevels[index]
    })
  },

  // 选择处理法
  onProcessChange(e) {
    const index = e.detail.value
    this.setData({
      'formData.process': this.data.processOptions[index]
    })
  },



  // 评分变化
  onRatingChange(e) {
    const rating = parseInt(e.currentTarget.dataset.rating)
    this.setData({
      'formData.rating': rating
    })
  },

  // 表单验证
  validateForm() {
    const { brand, name, origin } = this.data.formData
    
    if (!brand.trim()) {
      wx.showToast({
        title: '请输入品牌',
        icon: 'none'
      })
      return false
    }
    
    if (!name.trim()) {
      wx.showToast({
        title: '请输入咖啡名称',
        icon: 'none'
      })
      return false
    }
    
    if (!origin.trim()) {
      wx.showToast({
        title: '请输入产地',
        icon: 'none'
      })
      return false
    }
    
    return true
  },

  // 保存咖啡
  async saveCoffee() {
    console.log("save coffee")
    if (!this.validateForm()) {
      return
    }

    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const db = wx.cloud.database()
      const formData = { ...this.data.formData }
      
      // 转换价格为数字
      if (formData.price) {
        formData.price = parseFloat(formData.price)
      }

      // 转换研磨度为数字
      if (formData.grindSize) {
        formData.grindSize = parseInt(formData.grindSize)
      }

      if (this.data.isEdit) {
        delete formData._id;
        delete formData._openid;
        // 更新现有记录
        console.log("formData", formData, "coffeeid", this.data.coffeeId)

        await db.collection('coffee_atlas').doc(this.data.coffeeId).update({
          data: {
            ...formData,
            updateTime: new Date()
          }
        })
        
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        // 创建新记录
        await db.collection('coffee_atlas').add({
          data: {
            ...formData,
            createTime: new Date(),
            updateTime: new Date()
          }
        })
        
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
      }

      this.setData({ loading: false })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('保存失败', error)
      this.setData({ loading: false })
      
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 取消操作
  cancel() {
    wx.navigateBack()
  }
})
