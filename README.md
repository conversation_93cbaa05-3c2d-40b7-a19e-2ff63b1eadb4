# 我的咖啡 - 微信小程序

一个专业的咖啡豆管理微信小程序，帮助咖啡爱好者记录和管理自己的咖啡收藏。

## 功能特性

### ✅ 用户系统
- 微信登录授权
- 用户信息管理
- 云端数据同步
- 多用户数据隔离

### ✅ 咖啡图谱
- 咖啡豆信息记录（品牌、产地、品种、处理法等）
- 风味描述和个人评价
- 评分系统（1-5星）
- 搜索功能（支持品牌、名称、产地等多维度搜索）
- 添加到豆仓功能

### ✅ 豆仓管理
- 购买记录管理
- 状态跟踪（未开封、已开封、已饮尽）
- 日期记录（购买、开封、饮尽时间）
- 重量和价格记录
- 备注功能

### ✅ 数据统计
- 咖啡图谱总数
- 豆仓存储数量
- 总花费统计
- 平均评分
- 近期活动统计

### ✅ 现代化设计
- 咖啡主题色彩设计
- 响应式布局
- 流畅的交互动画
- 直观的用户界面

## 项目结构

```
mycoffee/
├── app.js                      # 小程序主逻辑（云开发初始化、用户管理）
├── app.json                    # 全局配置（页面路由、TabBar、云开发）
├── app.wxss                    # 全局样式（主题色彩、通用组件）
├── project.config.json         # 项目配置（云函数、AppID）
├── sitemap.json               # 搜索优化配置
├── cloudfunctions/            # 云函数目录
│   └── login/                 # 登录云函数
│       ├── index.js
│       └── package.json
├── pages/                     # 页面目录
│   ├── index/                 # 首页（登录、数据概览、快捷操作）
│   ├── atlas/                 # 咖啡图谱（列表、搜索、详情）
│   ├── storage/               # 豆仓管理（状态管理、记录查看）
│   ├── profile/               # 个人中心（统计、设置、帮助）
│   ├── coffee-form/           # 咖啡表单（添加/编辑咖啡）
│   └── storage-form/          # 豆仓表单（添加/编辑记录）
├── utils/                     # 工具函数
│   └── util.js               # 日期格式化、数据验证、工具函数
└── images/                    # 图片资源
    └── README.md
```

## 数据库设计

### 用户表 (users)
```javascript
{
  _id: "用户ID",
  _openid: "微信OpenID",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

### 咖啡图谱表 (coffee_atlas)
```javascript
{
  _id: "咖啡ID",
  _openid: "用户OpenID",
  brand: "品牌",
  name: "咖啡名称",
  variety: "品种",
  origin: "产地",
  estate: "庄园",
  process: "处理法",
  roastLevel: "烘焙度",
  flavor: "风味描述",
  price: "价格",
  grindSize: "推荐研磨度",
  rating: "评分(1-5)",
  review: "个人评价",
  purchaseLink: "购买链接",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

### 豆仓表 (coffee_storage)
```javascript
{
  _id: "记录ID",
  _openid: "用户OpenID",
  coffeeId: "关联咖啡ID",
  coffeeName: "咖啡名称",
  brand: "品牌",
  purchaseDate: "购买日期",
  openDate: "开封日期",
  finishDate: "饮尽日期",
  weight: "重量(g)",
  price: "价格",
  status: "状态(unopened/opened/finished)",
  notes: "备注",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

## 开发环境配置

### 1. 前置要求
- 微信开发者工具
- 微信小程序账号
- 微信云开发环境

### 2. 项目配置
1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd mycoffee
   ```

2. **配置AppID**
   - 在 `project.config.json` 中将 `appid` 改为你的小程序 AppID

3. **配置云开发**
   - 在微信开发者工具中开通云开发
   - 创建云开发环境
   - 部署云函数：右键 `cloudfunctions/login` → 上传并部署

4. **配置数据库**
   - 在云开发控制台创建以下集合：
     - `users`
     - `coffee_atlas`
     - `coffee_storage`
   - 设置数据库权限为"仅创建者可读写"

5. **添加图标资源**
   - 在 `images/` 目录下添加 TabBar 图标
   - 参考 `images/README.md` 中的规格要求

### 3. 运行项目
1. 在微信开发者工具中打开项目
2. 编译并预览
3. 使用微信扫码在手机上测试

## 技术栈

- **前端框架**: 微信小程序原生框架
- **云服务**: 微信云开发
- **数据库**: 云数据库
- **云函数**: Node.js
- **样式**: WXSS (支持CSS3)
- **状态管理**: 页面级状态管理

## 核心功能实现

### 用户登录流程
1. 用户点击登录按钮
2. 调用 `wx.login()` 获取临时登录凭证
3. 调用云函数 `login` 获取 OpenID
4. 调用 `wx.getUserProfile()` 获取用户信息
5. 保存用户信息到云数据库和本地存储

### 数据同步机制
- 所有数据通过云数据库存储
- 使用 OpenID 进行用户数据隔离
- 支持离线查看（本地缓存）
- 实时同步更新

### 搜索功能
- 支持多字段模糊搜索
- 实时搜索结果更新
- 搜索历史记录（计划中）

## 部署说明

### 开发环境
1. 在微信开发者工具中直接运行
2. 使用云开发测试环境

### 生产环境
1. 配置生产环境的云开发环境ID
2. 在 `app.js` 中设置正确的环境ID
3. 提交代码审核
4. 发布上线

## 后续开发计划

### 短期计划
- [ ] 数据导出功能
- [ ] 数据备份与恢复
- [ ] 搜索历史记录
- [ ] 批量操作功能

### 中期计划
- [ ] 咖啡推荐算法
- [ ] 社区功能
- [ ] 咖啡店地图集成
- [ ] 价格趋势分析
- [ ] 库存预警功能

### 长期计划
- [ ] AI 风味识别
- [ ] 个性化推荐系统
- [ ] 多平台支持
- [ ] 企业版功能

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/mycoffee]

## 致谢

感谢所有为这个项目做出贡献的开发者和咖啡爱好者们！
