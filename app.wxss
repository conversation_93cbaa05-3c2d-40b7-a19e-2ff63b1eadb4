/**app.wxss**/
/* 全局变量 */
page {
  --primary-color: #8B4513; /* 深棕色 */
  --secondary-color: #D2691E; /* 橙棕色 */
  --accent-color: #CD853F; /* 秘鲁色 */
  --background-color: #FFFFFF; /* 纯白色 */
  --card-background: #FFFFFF;
  --text-primary: #3E2723; /* 深棕色文字 */
  --text-secondary: #6D4C41; /* 中等棕色文字 */
  --text-light: #8D6E63; /* 浅棕色文字 */
  --border-color: #EFEBE9;
  --shadow: 0 2rpx 12rpx rgba(139, 69, 19, 0.1);

  background-color: var(--background-color);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  width: 100%;
  box-sizing: border-box; /* 确保padding/border不额外增加宽度 */
}

.container {
  min-height: 100vh;
  background-color: var(--background-color);
}

/* 通用按钮样式 */
.btn {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 6rpx rgba(139, 69, 19, 0.2);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:active {
  background-color: var(--primary-color);
  color: white;
}

/* 卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 24rpx;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.card-content {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 输入框样式 */
.input {
  background-color: var(--background-color);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  color: var(--text-primary);
  transition: border-color 0.3s ease;
}

.input:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* 标签样式 */
.tag {
  display: inline-block;
  background-color: var(--accent-color);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin: 4rpx;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: var(--border-color);
  margin: 32rpx 0;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

.margin-top {
  margin-top: 32rpx;
}

.margin-bottom {
  margin-bottom: 32rpx;
}

.padding {
  padding: 32rpx;
}

.padding-horizontal {
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.padding-vertical {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}

/* 页面标题 */
.page-title {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  text-align: center;
  margin: 48rpx 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
  color: var(--text-light);
}

.empty-state-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-state-text {
  font-size: 32rpx;
  margin-bottom: 48rpx;
}

/* 在自定义 TabBar 组件的样式文件（.wxss）中 */
.tabbar {
  padding-bottom: env(safe-area-inset-bottom); /* 适配 iOS 安全区域 */
  background-color: #FFFFFF; /* 与系统底色一致 */
}
