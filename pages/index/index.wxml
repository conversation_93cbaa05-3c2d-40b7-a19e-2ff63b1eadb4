<!--pages/index/index.wxml-->
<import src="../../templates/coffee-detail-modal.wxml"/>

<view class="container">
  <!-- 未登录状态 -->
  <view wx:if="{{!isLoggedIn}}" class="login-section">
    <view class="welcome-header">
      <view class="coffee-icon">☕</view>
      <view class="welcome-title">欢迎来到我的咖啡</view>
      <view class="welcome-subtitle">专业的咖啡豆管理工具</view>
    </view>

    <view class="features">
      <view class="feature-item">
        <view class="feature-icon">📊</view>
        <view class="feature-text">咖啡图谱管理</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🏭</view>
        <view class="feature-text">豆仓存储记录</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">📊</view>
        <view class="feature-text">数据统计分析</view>
      </view>
    </view>

    <view class="login-btn-area">
      <button class="btn login-btn" bindtap="initPage" loading="{{loading}}">
        登录开始使用
      </button>
    </view>
  </view>

  <!-- 已登录状态 -->
  <view wx:else class="dashboard">
    <!-- 用户信息 -->
    <view class="user-header">
      <view class="user-greeting">
        <view class="greeting-text">你好，</view>
        <view class="user-name">{{userInfo.nickName}}</view>
      </view>
      <view class="user-welcome">开始你的咖啡之旅吧！</view>
    </view>

    <!-- 统计数据 -->
    <view class="stats-section">
      <view class="stats-title">我的数据</view>
      <view class="stats-grid">
        <view class="stat-item" bindtap="navigateToAtlas">
          <view class="stat-number">{{stats.totalCoffees}}</view>
          <view class="stat-label">咖啡图谱</view>
        </view>
        <view class="stat-item" bindtap="navigateToStorage">
          <view class="stat-number">{{stats.totalStorage}}</view>
          <view class="stat-label">豆仓存储</view>
        </view>
      </view>
    </view>

    <!-- 快捷添加 -->
    <view class="quick-add-section">
      <button class="quick-add-btn" bindtap="navigateToAddCoffee">
        <view class="add-icon">+</view>
        <view class="add-text">添加咖啡豆</view>
      </button>
    </view>

    <!-- 最近添加的咖啡 -->
    <view class="recent-coffees">
      <view class="recent-title">最近添加</view>

      <!-- 加载状态 -->
      <view wx:if="{{recentLoading}}" class="recent-loading">
        <text>加载中...</text>
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{recentCoffees.length === 0}}" class="recent-empty">
        <view class="empty-icon">☕</view>
        <view class="empty-text">还没有咖啡记录</view>
        <view class="empty-hint">点击上方按钮添加你的第一个咖啡</view>
      </view>

      <!-- 咖啡列表 -->
      <view wx:else class="recent-list">
        <view
          wx:for="{{recentCoffees}}"
          wx:key="_id"
          class="recent-coffee-item"
          data-coffee="{{item}}"
          bindtap="viewCoffeeDetail"
        >
          <view class="coffee-header">
            <view class="coffee-brand">{{item.brand}}</view>
            <view class="coffee-price">¥{{item.price}}</view>
          </view>
          <view class="coffee-name">{{item.name}}</view>
          <view class="coffee-info">
            <text class="info-item">{{item.origin}}</text>
            <text class="info-item">{{item.variety}}</text>
            <text class="info-item">{{item.roastLevel}}</text>
          </view>
          <view class="coffee-flavor">{{item.flavor}}</view>
          <view class="coffee-rating">
            <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{item.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用咖啡详情弹窗模板 -->
  <template is="coffee-detail-modal" data="{{showDetail, selectedCoffee, showActions: true}}"/>

</view>
