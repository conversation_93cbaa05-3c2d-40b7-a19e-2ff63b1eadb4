/**pages/coffee-form/coffee-form.wxss**/

.form-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.form-header {
  padding: 32rpx;
  background-color: var(--card-background);
  border-bottom: 1rpx solid var(--border-color);
}

.form-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

.form-body {
  flex: 1;
  padding: 32rpx;
}

.form-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: calc(100% - 64rpx);
  height: 88rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  color: var(--text-primary);
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-picker {
  width: calc(100% - 64rpx);
  height: 88rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  color: var(--text-primary);
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.form-picker::after {
  content: '>';
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  color: var(--text-light);
  font-size: 24rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  color: var(--text-primary);
  transition: border-color 0.3s ease;
}

.form-textarea:focus {
  border-color: var(--primary-color);
}

.rating-input {
  display: flex;
  gap: 8rpx;
  padding: 16rpx 0;
}

.star {
  font-size: 48rpx;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.star.active {
  opacity: 1;
}

.form-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background-color: var(--card-background);
  border-top: 1rpx solid var(--border-color);
}

.form-actions .btn {
  flex: 1;
  font-size: 32rpx;
  padding: 32rpx;
}
