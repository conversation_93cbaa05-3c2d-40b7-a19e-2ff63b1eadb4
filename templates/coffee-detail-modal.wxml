<template name="coffee-detail-modal">
  <!-- 咖啡详情弹窗 -->
  <view wx:if="{{showDetail}}" class="detail-modal" bindtap="closeDetail">
    <view class="detail-content" catchtap="">
      <view class="detail-header">
        <view class="detail-title">{{selectedCoffee.name}}</view>
        <view class="detail-close" bindtap="closeDetail">✕</view>
      </view>

      <scroll-view class="detail-body" scroll-y>
        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="info-row">
            <text class="label">品牌：</text>
            <text class="value">{{selectedCoffee.brand}}</text>
          </view>
          <view class="info-row">
            <text class="label">产地：</text>
            <text class="value">{{selectedCoffee.origin}}</text>
          </view>
          <view class="info-row">
            <text class="label">品种：</text>
            <text class="value">{{selectedCoffee.variety}}</text>
          </view>
          <view class="info-row">
            <text class="label">处理法：</text>
            <text class="value">{{selectedCoffee.process}}</text>
          </view>
          <view class="info-row">
            <text class="label">烘焙度：</text>
            <text class="value">{{selectedCoffee.roastLevel}}</text>
          </view>
          <view class="info-row">
            <text class="label">价格：</text>
            <text class="value">¥{{selectedCoffee.price}}</text>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">风味描述</view>
          <view class="flavor-text">{{selectedCoffee.flavor}}</view>
        </view>

        <view class="detail-section">
          <view class="section-title">个人评价</view>
          <view class="rating-display">
            <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{selectedCoffee.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
          </view>
          <view class="review-text">{{selectedCoffee.review}}</view>
        </view>

        <view wx:if="{{selectedCoffee.grindSize}}" class="detail-section">
          <view class="section-title">推荐研磨度</view>
          <view class="grind-text">{{selectedCoffee.grindSize}}</view>
        </view>

        <view wx:if="{{selectedCoffee.purchaseLink}}" class="detail-section">
          <view class="section-title">购买链接</view>
          <view class="link-text">{{selectedCoffee.purchaseLink}}</view>
        </view>
      </scroll-view>

      <!-- 操作按钮区域 - 可选显示 -->
      <view wx:if="{{showActions}}" class="detail-actions">
        <button class="btn btn-outline" bindtap="addToStorage">加到豆仓</button>
        <button class="btn btn-outline" bindtap="editCoffee">编辑</button>
        <button class="btn btn-outline delete-btn" bindtap="deleteCoffee">删除</button>
      </view>
    </view>
  </view>
</template>
