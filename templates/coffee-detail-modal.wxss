/* 咖啡详情弹窗模板样式 */

/* 详情弹窗 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-content {
  background-color: var(--card-background);
  border-radius: 24rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.detail-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-light);
}

.detail-body {
  flex: 1;
  padding: 32rpx;
}

.detail-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
}

.label {
  font-size: 28rpx;
  color: var(--text-secondary);
  width: 120rpx;
}

.value {
  font-size: 28rpx;
  color: var(--text-primary);
  flex: 1;
}

.flavor-text,
.review-text,
.grind-text,
.link-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

.rating-display {
  display: flex;
  gap: 4rpx;
  margin-bottom: 16rpx;
}

.detail-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--border-color);
}

.detail-actions .btn {
  flex: 1;
  font-size: 28rpx;
  padding: 20rpx 16rpx;
}

.delete-btn {
  color: #ff4757 !important;
  border-color: #ff4757 !important;
}

.delete-btn:active {
  background-color: #ff4757 !important;
  color: white !important;
}
