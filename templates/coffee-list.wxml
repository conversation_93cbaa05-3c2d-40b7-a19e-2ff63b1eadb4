<template name="coffee-list">
  <!-- 咖啡列表模板 -->
  <view class="{{listClass || 'coffee-list'}}">
    <!-- 空状态 -->
    <view wx:if="{{coffeeList.length === 0}}" class="empty-state">
      <view class="empty-state-icon">☕</view>
      <view class="empty-state-text">{{emptyText || '还没有咖啡记录'}}</view>
      <view wx:if="{{emptyHint}}" class="empty-state-hint">{{emptyHint}}</view>
      <button wx:if="{{showAddButton}}" class="btn" bindtap="{{addButtonAction || 'addCoffee'}}">
        {{addButtonText || '添加第一个咖啡'}}
      </button>
    </view>

    <!-- 咖啡项列表 -->
    <view wx:else>
      <view
        wx:for="{{coffeeList}}"
        wx:key="_id"
        class="{{itemClass || 'coffee-item'}}"
        data-coffee="{{item}}"
        bindtap="{{itemTapAction || 'viewCoffeeDetail'}}"
      >
        <view class="coffee-header">
          <view class="coffee-brand">{{item.brand}}</view>
          <view class="coffee-price" wx:if="{{item.price}}">¥{{item.price}}</view>
        </view>
        <view class="coffee-name">{{item.name}}</view>
        <view class="coffee-info">
          <text wx:if="{{item.origin}}" class="info-item">{{item.origin}}</text>
          <text wx:if="{{item.variety}}" class="info-item">{{item.variety}}</text>
          <text wx:if="{{item.roastLevel}}" class="info-item">{{item.roastLevel}}</text>
        </view>
        <view wx:if="{{item.flavor}}" class="coffee-flavor">{{item.flavor}}</view>
        <view class="coffee-rating">
          <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{item.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
        </view>
      </view>
    </view>
  </view>
</template>
