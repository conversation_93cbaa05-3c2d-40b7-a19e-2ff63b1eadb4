<!--pages/atlas/atlas.wxml-->
<import src="../../templates/coffee-detail-modal.wxml"/>

<view class="container">
  <!-- 头部操作栏 -->
  <view class="header">
    <view class="header-title">咖啡图谱</view>
    <view class="header-actions">
      <view class="action-btn" bindtap="toggleSearch">
        <text class="icon">🔍</text>
      </view>
      <view class="action-btn" bindtap="addCoffee">
        <text class="icon">➕</text>
      </view>
    </view>
  </view>

  <!-- 搜索框 -->
  <view wx:if="{{showSearch}}" class="search-section">
    <input
      class="search-input"
      placeholder="搜索品牌、名称、产地、品种、风味..."
      value="{{searchKeyword}}"
      bindinput="onSearchInput"
    />
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 咖啡列表 -->
  <view wx:else class="coffee-list">
    <view wx:if="{{filteredList.length === 0}}" class="empty-state">
      <view class="empty-state-icon">☕</view>
      <view class="empty-state-text">
        {{searchKeyword ? '没有找到相关咖啡' : '还没有添加咖啡记录'}}
      </view>
      <button wx:if="{{!searchKeyword}}" class="btn" bindtap="addCoffee">
        添加第一个咖啡
      </button>
    </view>

    <view wx:else>
      <view
        wx:for="{{filteredList}}"
        wx:key="_id"
        class="coffee-item"
        data-coffee="{{item}}"
        bindtap="viewCoffeeDetail"
      >
        <view class="coffee-header">
          <view class="coffee-brand">{{item.brand}}</view>
          <view class="coffee-price">¥{{item.price}}</view>
        </view>
        <view class="coffee-name">{{item.name}}</view>
        <view class="coffee-info">
          <text class="info-item">{{item.origin}}</text>
          <text class="info-item">{{item.variety}}</text>
          <text class="info-item">{{item.roastLevel}}</text>
        </view>
        <view class="coffee-flavor">{{item.flavor}}</view>
        <view class="coffee-rating">
          <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{item.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用咖啡详情弹窗模板 -->
  <template is="coffee-detail-modal" data="{{showDetail, selectedCoffee, showActions: true}}"/>
</view>
